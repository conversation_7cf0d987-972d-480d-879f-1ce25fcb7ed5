import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Calendar, Loader2, Truck } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSpiralLoads, useAddSpiralLoad } from "@/hooks/useSpiralLoads";
import { format } from "date-fns";

const spiralLoadSchema = z.object({
  date: z.string().min(1, "Date is required"),
  dnote_no: z.string().min(1, "Delivery note number is required"),
  supplier: z.string().min(1, "Supplier is required"),
  mine: z.string().min(1, "Mine is required"),
  wet_weight: z.number().min(0, "Wet weight must be positive"),
  dry_weight: z.number().min(0, "Dry weight must be positive"),
  carbon: z.number().min(0, "Carbon must be positive")
});

type SpiralLoadFormValues = z.infer<typeof spiralLoadSchema>;

export const SpiralLoadsPage = () => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  const { data: spiralLoads = [], isLoading, error } = useSpiralLoads();
  const addSpiralLoadMutation = useAddSpiralLoad();

  const form = useForm<SpiralLoadFormValues>({
    resolver: zodResolver(spiralLoadSchema),
    defaultValues: {
      date: format(new Date(), "yyyy-MM-dd"),
      dnote_no: "",
      supplier: "",
      mine: "",
      wet_weight: 0,
      dry_weight: 0,
      carbon: 0
    }
  });

  const onSubmit = (data: SpiralLoadFormValues) => {
    addSpiralLoadMutation.mutate(data, {
      onSuccess: () => {
        toast.success("Spiral load recorded successfully!");
        form.reset({
          date: format(new Date(), "yyyy-MM-dd"),
          dnote_no: "",
          supplier: "",
          mine: "",
          wet_weight: 0,
          dry_weight: 0,
          carbon: 0
        });
        setIsFormVisible(false);
      },
      onError: (error) => {
        toast.error(`Failed to record spiral load: ${error.message}`);
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Spiral Loads</h1>
          <p className="text-slate-600">Track spiral coal deliveries</p>
        </div>
        <Button onClick={() => setIsFormVisible(!isFormVisible)} className="flex items-center gap-2">
          <Plus size={20} />
          {isFormVisible ? "Cancel" : "Add Load"}
        </Button>
      </div>

      {isFormVisible && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck size={20} />
              Record Spiral Load
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date" className="flex items-center gap-2">
                    <Calendar size={16} />
                    Date
                  </Label>
                  <Input id="date" type="date" {...form.register("date")} />
                  {form.formState.errors.date && <p className="text-sm text-red-600">{form.formState.errors.date.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dnote_no">Delivery Note No.</Label>
                  <Input id="dnote_no" {...form.register("dnote_no")} placeholder="Enter delivery note number" />
                  {form.formState.errors.dnote_no && <p className="text-sm text-red-600">{form.formState.errors.dnote_no.message}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="supplier">Supplier</Label>
                  <Input id="supplier" {...form.register("supplier")} placeholder="Enter supplier name" />
                  {form.formState.errors.supplier && <p className="text-sm text-red-600">{form.formState.errors.supplier.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="mine">Mine</Label>
                  <Input id="mine" {...form.register("mine")} placeholder="Enter mine name" />
                  {form.formState.errors.mine && <p className="text-sm text-red-600">{form.formState.errors.mine.message}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="wet_weight">Wet Weight (tons)</Label>
                  <Input id="wet_weight" type="number" step="0.01" {...form.register("wet_weight", { valueAsNumber: true })} />
                  {form.formState.errors.wet_weight && <p className="text-sm text-red-600">{form.formState.errors.wet_weight.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dry_weight">Dry Weight (tons)</Label>
                  <Input id="dry_weight" type="number" step="0.01" {...form.register("dry_weight", { valueAsNumber: true })} />
                  {form.formState.errors.dry_weight && <p className="text-sm text-red-600">{form.formState.errors.dry_weight.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="carbon">Carbon (%)</Label>
                  <Input id="carbon" type="number" step="0.01" {...form.register("carbon", { valueAsNumber: true })} />
                  {form.formState.errors.carbon && <p className="text-sm text-red-600">{form.formState.errors.carbon.message}</p>}
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button type="submit" disabled={addSpiralLoadMutation.isPending} className="flex items-center gap-2">
                  {addSpiralLoadMutation.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                  Record Load
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsFormVisible(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Recent Spiral Loads</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <p>Error loading spiral loads: {error.message}</p>
            </div>
          ) : spiralLoads.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <Truck className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No spiral loads recorded yet.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {spiralLoads.slice(0, 10).map(load => (
                <div key={load.id} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar size={16} className="text-slate-500" />
                      <span className="font-medium">{format(new Date(load.date), "MMM dd, yyyy")}</span>
                    </div>
                    <div className="text-sm">
                      <span className="font-semibold">{load.dnote_no}</span> - {load.supplier}
                    </div>
                    <div className="text-sm text-slate-600">
                      <span>{load.mine}</span>
                    </div>
                    <div className="text-sm">
                      <span>Wet: {load.wet_weight}t</span>
                      <span className="ml-2">Dry: {load.dry_weight}t</span>
                      <span className="ml-2">C: {load.carbon}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
