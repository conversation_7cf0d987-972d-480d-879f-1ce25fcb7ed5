
import { useState, useEffect } from "react";
import { useUser } from "@/contexts/UserContext";
import { Sidebar } from "@/components/layout/Sidebar";
import { Header } from "@/components/layout/Header";
import { DashboardContent } from "@/components/dashboard/DashboardContent";
import { DehackingPage } from "@/components/pages/DehackingPage";
import EmployeesPage from "@/components/pages/EmployeesPage";
import { KilnsPage } from "@/components/pages/KilnsPage";
import { PalletTrackingPage } from "@/components/pages/PalletTrackingPage";
import { BrickTypesPage } from "@/components/pages/BrickTypesPage";
import { PaymentsPage } from "@/components/pages/PaymentsPage";
import { ReportsPage } from "@/components/pages/ReportsPage";
import { FuelManagementPage } from "@/components/pages/FuelManagementPage";
import { SettingsPage } from "@/components/pages/SettingsPage";
import { AssetsPage } from "@/components/pages/AssetsPage";
import TeamManagementPage from "@/components/pages/TeamManagementPage";
import { HacklineCountPage } from "@/components/pages/HacklineCountPage";
import { FinishedProductsPage } from "@/components/pages/FinishedProductsPage";
import { CarbonSpiralTrackerPage } from "@/components/pages/CarbonSpiralTrackerPage";

export type MenuItem =
  | "dashboard"
  | "dehacking"
  | "employees"
  | "team-management"
  | "kilns"
  | "pallet-tracking"
  | "brick-types"
  | "payments"
  | "reports"
  | "fuel-management"
  | "assets"
  | "hackline-count"
  | "finished-products"
  | "carbon-spiral-tracker"
  | "settings";

const Index = () => {
  const [activeMenuItem, setActiveMenuItem] = useState<MenuItem>("dashboard");
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { canAccessMenuItem } = useUser();

  // Check hash for navigation from quick access cards
  useEffect(() => {
    const hash = window.location.hash.substring(1);
    if (hash === 'pallet-tracking' && canAccessMenuItem('pallet-tracking')) {
      setActiveMenuItem('pallet-tracking');
      window.location.hash = ''; // Clear the hash
    }
  }, [canAccessMenuItem]);

  // Redirect to dashboard if user tries to access unauthorized page
  useEffect(() => {
    if (!canAccessMenuItem(activeMenuItem)) {
      setActiveMenuItem("dashboard");
    }
  }, [activeMenuItem, canAccessMenuItem]);

  const renderContent = () => {
    // Check if user has access to the current menu item
    if (!canAccessMenuItem(activeMenuItem)) {
      return <DashboardContent />;
    }

    switch (activeMenuItem) {
      case "dashboard":
        return <DashboardContent />;
      case "dehacking":
        return <DehackingPage />;
      case "employees":
        return <EmployeesPage />;
      case "team-management":
        return <TeamManagementPage />;
      case "kilns":
        return <KilnsPage />;
      case "pallet-tracking":
        return <PalletTrackingPage />;
      case "brick-types":
        return <BrickTypesPage />;
      case "payments":
        return <PaymentsPage />;
      case "reports":
        return <ReportsPage />;
      case "fuel-management":
        return <FuelManagementPage />;
      case "assets":
        return <AssetsPage />;
      case "hackline-count":
        return <HacklineCountPage />;
      case "finished-products":
        return <FinishedProductsPage />;
      case "carbon-spiral-tracker":
        return <CarbonSpiralTrackerPage />;
      case "settings":
        return <SettingsPage />;
      default:
        return <DashboardContent />;
    }
  };

  return (
    <div className="min-h-screen bg-slate-100 flex w-full">
      <Sidebar
        activeMenuItem={activeMenuItem}
        onMenuItemClick={setActiveMenuItem}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />
      <div className="flex-1 flex flex-col min-w-0">
        <Header
          onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
        <main className="flex-1 p-3 sm:p-4 lg:p-6 overflow-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default Index;
