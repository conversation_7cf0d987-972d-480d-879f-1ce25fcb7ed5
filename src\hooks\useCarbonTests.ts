
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface CarbonTest {
  id: number;
  created_at: string;
  date: string;
  time: string;
  test_type: 'maxi' | 'imperial' | 'spiral' | 'ash';
  average_carbon: number;
  average_sulphur: number;
  user_id: string;
}

export interface NewCarbonTest {
  date: string;
  time: string;
  test_type: 'maxi' | 'imperial' | 'spiral' | 'ash';
  average_carbon: number;
  average_sulphur: number;
}

export function useCarbonTests() {
  return useQuery({
    queryKey: ["carbonTests"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("carbon_tests")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching carbon tests:", error);
        throw new Error(error.message);
      }

      return (data || []) as CarbonTest[];
    },
  });
}

export function useAddCarbonTest() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (newTest: NewCarbonTest) => {
      if (!currentUser?.id) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("carbon_tests")
        .insert({
          date: newTest.date,
          time: newTest.time,
          test_type: newTest.test_type,
          average_carbon: newTest.average_carbon,
          average_sulphur: newTest.average_sulphur,
          user_id: currentUser.id,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding carbon test:", error);
        throw new Error(error.message);
      }

      return data as CarbonTest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["carbonTests"] });
    },
  });
}
