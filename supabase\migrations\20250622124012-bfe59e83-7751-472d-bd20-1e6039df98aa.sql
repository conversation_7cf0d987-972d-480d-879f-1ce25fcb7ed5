
-- Create table for carbon tests
CREATE TABLE public.carbon_tests (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  date DATE NOT NULL,
  time TIME NOT NULL,
  test_type TEXT NOT NULL CHECK (test_type IN ('maxi', 'imperial', 'spiral', 'ash')),
  average_carbon NUMERIC(5,2) NOT NULL,
  average_sulphur NUMERIC(5,2) NOT NULL,
  user_id UUID NOT NULL REFERENCES public.users(id)
);

-- Create table for spiral loads
CREATE TABLE public.spiral_loads (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  date DATE NOT NULL,
  dnote_no TEXT NOT NULL,
  supplier TEXT NOT NULL,
  mine TEXT NOT NULL,
  wet_weight NUMERIC(10,2) NOT NULL,
  dry_weight NUMERIC(10,2) NOT NULL,
  carbon NUMERIC(5,2) NOT NULL,
  user_id UUID NOT NULL REFERENCES public.users(id)
);

-- Create table for breakdowns
CREATE TABLE public.breakdowns (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  date DATE NOT NULL,
  time TIME NOT NULL,
  breakdown_comment TEXT NOT NULL,
  stop_time TIME NOT NULL,
  start_time TIME NOT NULL,
  user_id UUID NOT NULL REFERENCES public.users(id)
);

-- Add Row Level Security
ALTER TABLE public.carbon_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.spiral_loads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.breakdowns ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for carbon_tests
CREATE POLICY "Users can view all carbon tests" ON public.carbon_tests
  FOR SELECT USING (true);

CREATE POLICY "Users can insert carbon tests" ON public.carbon_tests
  FOR INSERT WITH CHECK (auth.uid()::text IN (SELECT id::text FROM public.users));

-- Create RLS policies for spiral_loads
CREATE POLICY "Users can view all spiral loads" ON public.spiral_loads
  FOR SELECT USING (true);

CREATE POLICY "Users can insert spiral loads" ON public.spiral_loads
  FOR INSERT WITH CHECK (auth.uid()::text IN (SELECT id::text FROM public.users));

-- Create RLS policies for breakdowns
CREATE POLICY "Users can view all breakdowns" ON public.breakdowns
  FOR SELECT USING (true);

CREATE POLICY "Users can insert breakdowns" ON public.breakdowns
  FOR INSERT WITH CHECK (auth.uid()::text IN (SELECT id::text FROM public.users));

-- Create indexes for better performance
CREATE INDEX idx_carbon_tests_date ON public.carbon_tests(date);
CREATE INDEX idx_spiral_loads_date ON public.spiral_loads(date);
CREATE INDEX idx_breakdowns_date ON public.breakdowns(date);
