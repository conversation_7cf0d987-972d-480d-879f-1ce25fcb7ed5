import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface ChamberFireStatus {
  id: number;
  kiln_id: string;
  chamber_number: number;
  is_burning: boolean;
  updated_by: string;
  created_at: string;
  updated_at: string;
}

export function useChamberFireStatus() {
  return useQuery({
    queryKey: ["chamberFireStatus"],
    queryFn: async () => {
      // Table doesn't exist yet, return empty array
      console.log("Chamber fire status table not yet created");
      return [] as ChamberFireStatus[];
      
      // TODO: Uncomment when table is created
      // try {
      //   const { data, error } = await supabase
      //     .from("chamber_fire_status")
      //     .select("*")
      //     .order("updated_at", { ascending: false });

      //   if (error) {
      //     console.error("Error fetching chamber fire status:", error);
      //     throw new Error(error.message);
      //   }

      //   return (data || []) as ChamberFireStatus[];
      // } catch (error) {
      //   console.error("Error in chamber fire status query:", error);
      //   return [];
      // }
    },
    retry: false,
  });
}

export function useUpdateChamberFireStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (update: { id: number; is_burning: boolean; updated_by: string }) => {
      // Table doesn't exist yet, just return the update
      console.log("Chamber fire status update (table not created):", update);
      return update;
      
      // TODO: Uncomment when table is created
      // try {
      //   const { data, error } = await supabase
      //     .from("chamber_fire_status")
      //     .update({
      //       is_burning: update.is_burning,
      //       updated_by: update.updated_by,
      //       updated_at: new Date().toISOString()
      //     })
      //     .eq("id", update.id)
      //     .select()
      //     .single();

      //   if (error) {
      //     console.error("Error updating chamber fire status:", error);
      //     throw new Error(error.message);
      //   }

      //   return data as ChamberFireStatus;
      // } catch (error) {
      //   console.error("Error in chamber fire status update:", error);
      //   throw error;
      // }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chamberFireStatus"] });
    },
  });
}
