-- Fix RLS policies for carbon_tests, spiral_loads, and breakdowns tables
-- This migration removes the auth.uid() dependency and allows all operations
-- since the app uses custom authentication instead of Supabase Auth

-- Drop existing policies for carbon_tests
DROP POLICY IF EXISTS "Users can insert carbon tests" ON public.carbon_tests;

-- Create new policies for carbon_tests
CREATE POLICY "Users can insert carbon tests" ON public.carbon_tests
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update carbon tests" ON public.carbon_tests
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete carbon tests" ON public.carbon_tests
  FOR DELETE USING (true);

-- Drop existing policies for spiral_loads
DROP POLICY IF EXISTS "Users can insert spiral loads" ON public.spiral_loads;

-- Create new policies for spiral_loads
CREATE POLICY "Users can insert spiral loads" ON public.spiral_loads
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update spiral loads" ON public.spiral_loads
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete spiral loads" ON public.spiral_loads
  FOR DELETE USING (true);

-- Drop existing policies for breakdowns
DROP POLICY IF EXISTS "Users can insert breakdowns" ON public.breakdowns;

-- Create new policies for breakdowns
CREATE POLICY "Users can insert breakdowns" ON public.breakdowns
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update breakdowns" ON public.breakdowns
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete breakdowns" ON public.breakdowns
  FOR DELETE USING (true);
