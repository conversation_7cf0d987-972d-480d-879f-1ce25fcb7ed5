
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface Breakdown {
  id: number;
  created_at: string;
  date: string;
  time: string;
  breakdown_comment: string;
  stop_time: string;
  start_time: string;
  user_id: string;
}

export interface NewBreakdown {
  date: string;
  time: string;
  breakdown_comment: string;
  stop_time: string;
  start_time: string;
}

export function useBreakdowns() {
  return useQuery({
    queryKey: ["breakdowns"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("breakdowns")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching breakdowns:", error);
        throw new Error(error.message);
      }

      return (data || []) as Breakdown[];
    },
  });
}

export function useAddBreakdown() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (newBreakdown: NewBreakdown) => {
      if (!currentUser?.id) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("breakdowns")
        .insert({
          date: newBreakdown.date,
          time: newBreakdown.time,
          breakdown_comment: newBreakdown.breakdown_comment,
          stop_time: newBreakdown.stop_time,
          start_time: newBreakdown.start_time,
          user_id: currentUser.id,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding breakdown:", error);
        throw new Error(error.message);
      }

      return data as Breakdown;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breakdowns"] });
    },
  });
}
