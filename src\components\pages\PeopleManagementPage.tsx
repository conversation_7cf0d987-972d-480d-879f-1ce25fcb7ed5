import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Users, UserCheck } from "lucide-react";
import EmployeesPage from "./EmployeesPage";
import TeamManagementPage from "./TeamManagementPage";

export const PeopleManagementPage = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Users className="h-8 w-8 text-slate-600" />
        <div>
          <h1 className="text-3xl font-bold text-slate-800">People Management</h1>
          <p className="text-slate-600">Manage employees and teams</p>
        </div>
      </div>

      <Tabs defaultValue="employees" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="employees" className="flex items-center gap-2">
            <Users size={16} />
            Employees
          </TabsTrigger>
          <TabsTrigger value="teams" className="flex items-center gap-2">
            <UserCheck size={16} />
            Team Management
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="employees" className="mt-6">
          <EmployeesPage />
        </TabsContent>
        
        <TabsContent value="teams" className="mt-6">
          <TeamManagementPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};
