
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface SpiralLoad {
  id: number;
  created_at: string;
  date: string;
  dnote_no: string;
  supplier: string;
  mine: string;
  wet_weight: number;
  dry_weight: number;
  carbon: number;
  user_id: string;
}

export interface NewSpiralLoad {
  date: string;
  dnote_no: string;
  supplier: string;
  mine: string;
  wet_weight: number;
  dry_weight: number;
  carbon: number;
}

export function useSpiralLoads() {
  return useQuery({
    queryKey: ["spiralLoads"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("spiral_loads")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching spiral loads:", error);
        throw new Error(error.message);
      }

      return (data || []) as SpiralLoad[];
    },
  });
}

export function useAddSpiralLoad() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (newLoad: NewSpiralLoad) => {
      if (!currentUser?.id) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("spiral_loads")
        .insert({
          date: newLoad.date,
          dnote_no: newLoad.dnote_no,
          supplier: newLoad.supplier,
          mine: newLoad.mine,
          wet_weight: newLoad.wet_weight,
          dry_weight: newLoad.dry_weight,
          carbon: newLoad.carbon,
          user_id: currentUser.id,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding spiral load:", error);
        throw new Error(error.message);
      }

      return data as SpiralLoad;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["spiralLoads"] });
    },
  });
}
