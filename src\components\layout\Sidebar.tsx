
import { cn } from "@/lib/utils";
import { useUser } from "@/contexts/UserContext";
import { useIsMobile } from "@/hooks/use-mobile";
import { LayoutDashboard, Hammer, Users, Flame, Truck, Archive, CreditCard, FileText, Fuel, Settings, ChevronLeft, ChevronRight, Database, X, Calculator, Package, TestTube } from "lucide-react";
import { MenuItem } from "@/pages/Index";

interface SidebarProps {
  activeMenuItem: MenuItem;
  onMenuItemClick: (item: MenuItem) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
}

const menuItems = [{
  id: "dashboard" as MenuItem,
  label: "Dashboard",
  icon: LayoutDashboard
}, {
  id: "dehacking" as MenuItem,
  label: "Dehacking",
  icon: Hammer
}, {
  id: "employees" as MenuItem,
  label: "Employees",
  icon: Users
}, {
  id: "team-management" as MenuItem,
  label: "Team Management",
  icon: Users
}, {
  id: "kilns" as MenuItem,
  label: "Kilns",
  icon: Flame
}, {
  id: "pallet-tracking" as MenuItem,
  label: "Pallet Tracking",
  icon: Truck
}, {
  id: "brick-types" as MenuItem,
  label: "Brick Types",
  icon: Archive
}, {
  id: "payments" as MenuItem,
  label: "Payments",
  icon: CreditCard
}, {
  id: "reports" as MenuItem,
  label: "Reports",
  icon: FileText
}, {
  id: "fuel-management" as MenuItem,
  label: "Fuel Management",
  icon: Fuel
}, {
  id: "assets" as MenuItem,
  label: "Assets",
  icon: Database
}, {
  id: "hackline-count" as MenuItem,
  label: "Hackline Count",
  icon: Calculator
}, {
  id: "finished-products" as MenuItem,
  label: "Finished Products",
  icon: Package
}, {
  id: "carbon-spiral-tracker" as MenuItem,
  label: "Carbon & Spiral Tracker",
  icon: TestTube
}, {
  id: "settings" as MenuItem,
  label: "Settings",
  icon: Settings
}];

export const Sidebar = ({
  activeMenuItem,
  onMenuItemClick,
  collapsed,
  onToggleCollapse
}: SidebarProps) => {
  const {
    canAccessMenuItem
  } = useUser();
  const isMobile = useIsMobile();
  const handleMenuItemClick = (item: MenuItem) => {
    onMenuItemClick(item);
    // Auto-close sidebar on mobile after selection
    if (isMobile && !collapsed) {
      onToggleCollapse();
    }
  };
  const sidebarContent = <>
      <div className="p-4 border-b border-slate-700 flex items-center justify-between">
        {!collapsed && <h1 className="text-base sm:text-lg font-semibold text-white truncate">Worcester Bakstene</h1>}
        <button onClick={onToggleCollapse} className="p-1 rounded hover:bg-slate-700 transition-colors flex-shrink-0" aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}>
          {isMobile ? <X size={20} /> : collapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>

      <nav className="flex-1 p-3 sm:p-4 overflow-y-auto">
        <ul className="space-y-1 sm:space-y-2">
          {menuItems.map(item => {
          const Icon = item.icon;
          const isActive = activeMenuItem === item.id;
          const hasAccess = canAccessMenuItem(item.id);

          // Only render menu items that the user has access to
          if (!hasAccess) {
            return null;
          }
          return <li key={item.id}>
                <button onClick={() => handleMenuItemClick(item.id)} className={cn("w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors text-left touch-manipulation", isActive ? "bg-slate-700 text-white" : "text-slate-300 hover:text-white hover:bg-slate-700", "min-h-[44px]" // Ensure touch-friendly minimum height
            )}>
                  <Icon size={20} className="flex-shrink-0" />
                  {!collapsed && <span className="truncate text-sm sm:text-base">{item.label}</span>}
                </button>
              </li>;
        })}
        </ul>
      </nav>
    </>;

  // Mobile overlay sidebar
  if (isMobile) {
    return <>
        {/* Backdrop */}
        {!collapsed && <div className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" onClick={onToggleCollapse} />}

        {/* Sidebar */}
        <div className={cn("fixed top-0 left-0 h-full bg-slate-800 text-white transition-transform duration-300 flex flex-col z-50 lg:hidden", "w-64 sm:w-72", collapsed ? "-translate-x-full" : "translate-x-0")}>
          {sidebarContent}
        </div>
      </>;
  }

  // Desktop sidebar
  return <div className={cn("bg-slate-800 text-white transition-all duration-300 flex flex-col hidden lg:flex", collapsed ? "w-16" : "w-64")}>
      {sidebarContent}
    </div>;
};
