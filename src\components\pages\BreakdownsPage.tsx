import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Calendar, Clock, Loader2, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useBreakdowns, useAddBreakdown } from "@/hooks/useBreakdowns";
import { format } from "date-fns";
const breakdownSchema = z.object({
  date: z.string().min(1, "Date is required"),
  time: z.string().min(1, "Time is required"),
  breakdown_comment: z.string().min(1, "Breakdown comment is required"),
  stop_time: z.string().min(1, "Stop time is required"),
  start_time: z.string().min(1, "Start time is required")
});
type BreakdownFormValues = z.infer<typeof breakdownSchema>;
export const BreakdownsPage = () => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  const {
    data: breakdowns = [],
    isLoading,
    error
  } = useBreakdowns();
  const addBreakdownMutation = useAddBreakdown();
  const form = useForm<BreakdownFormValues>({
    resolver: zodResolver(breakdownSchema),
    defaultValues: {
      date: format(new Date(), "yyyy-MM-dd"),
      time: format(new Date(), "HH:mm"),
      breakdown_comment: "",
      stop_time: "",
      start_time: ""
    }
  });
  const onSubmit = (data: BreakdownFormValues) => {
    addBreakdownMutation.mutate(data, {
      onSuccess: () => {
        toast.success("Breakdown recorded successfully!");
        form.reset({
          date: format(new Date(), "yyyy-MM-dd"),
          time: format(new Date(), "HH:mm"),
          breakdown_comment: "",
          stop_time: "",
          start_time: ""
        });
        setIsFormVisible(false);
      },
      onError: error => {
        toast.error(`Failed to record breakdown: ${error.message}`);
      }
    });
  };
  return <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Breakdowns</h1>
          <p className="text-slate-600">Track testing breakdowns</p>
        </div>
        <Button onClick={() => setIsFormVisible(!isFormVisible)} className="flex items-center gap-2">
          <Plus size={20} />
          {isFormVisible ? "Cancel" : "Add Breakdown"}
        </Button>
      </div>

      {isFormVisible && <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle size={20} />
              Record Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date" className="flex items-center gap-2">
                    <Calendar size={16} />
                    Date
                  </Label>
                  <Input id="date" type="date" {...form.register("date")} />
                  {form.formState.errors.date && <p className="text-sm text-red-600">{form.formState.errors.date.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="time" className="flex items-center gap-2">
                    <Clock size={16} />
                    Time
                  </Label>
                  <Input id="time" type="time" {...form.register("time")} />
                  {form.formState.errors.time && <p className="text-sm text-red-600">{form.formState.errors.time.message}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="breakdown_comment">Breakdown Comment</Label>
                <Textarea id="breakdown_comment" {...form.register("breakdown_comment")} placeholder="Describe the breakdown..." rows={3} />
                {form.formState.errors.breakdown_comment && <p className="text-sm text-red-600">{form.formState.errors.breakdown_comment.message}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stop_time">Stop Time</Label>
                  <Input id="stop_time" type="time" {...form.register("stop_time")} />
                  {form.formState.errors.stop_time && <p className="text-sm text-red-600">{form.formState.errors.stop_time.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="start_time">Start Time</Label>
                  <Input id="start_time" type="time" {...form.register("start_time")} />
                  {form.formState.errors.start_time && <p className="text-sm text-red-600">{form.formState.errors.start_time.message}</p>}
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button type="submit" disabled={addBreakdownMutation.isPending} className="flex items-center gap-2">
                  {addBreakdownMutation.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                  Record Breakdown
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsFormVisible(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>}

      <Card>
        <CardHeader>
          <CardTitle>Recent Breakdowns</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
            </div> : error ? <div className="text-center py-8 text-red-500">
              <p>Error loading breakdowns: {error.message}</p>
            </div> : breakdowns.length === 0 ? <div className="text-center py-8 text-slate-500">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No breakdowns recorded yet.</p>
            </div> : <div className="space-y-3">
              {breakdowns.slice(0, 10).map(breakdown => <div key={breakdown.id} className="p-4 bg-slate-50 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Calendar size={16} className="text-slate-500" />
                        <span className="font-medium">{format(new Date(breakdown.date), "MMM dd, yyyy")}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock size={16} className="text-slate-500" />
                        <span>{breakdown.time}</span>
                      </div>
                    </div>
                    <div className="text-sm text-slate-600">
                      <span>Stop: {breakdown.stop_time}</span>
                      <span className="ml-2">Start: {breakdown.start_time}</span>
                    </div>
                  </div>
                  <p className="text-slate-700">{breakdown.breakdown_comment}</p>
                </div>)}
            </div>}
        </CardContent>
      </Card>
    </div>;
};