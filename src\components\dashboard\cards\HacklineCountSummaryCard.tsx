
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calculator, Loader2, Package } from "lucide-react";
import { TimeRange } from "../DashboardContent";
import { useHacklineCounts } from "@/hooks/useHacklineCounts";
import { isToday, isThisWeek, isThisMonth, startOfYear } from "date-fns";

interface HacklineCountSummaryCardProps {
  timeRange: TimeRange;
}

export const HacklineCountSummaryCard = ({ timeRange }: HacklineCountSummaryCardProps) => {
  const { data: hacklineCounts = [], isLoading } = useHacklineCounts();

  const timeFilter = (date: Date): boolean => {
    switch (timeRange) {
      case "today":
        return isToday(date);
      case "week":
        return isThisWeek(date, { weekStartsOn: 1 });
      case "month":
        return isThisMonth(date);
      case "year":
        return date >= startOfYear(new Date());
      default:
        return true;
    }
  };

  // Filter hackline counts based on time range
  const filteredCounts = hacklineCounts.filter(count => 
    timeFilter(new Date(count.date))
  );

  // Calculate totals
  const totalEntries = filteredCounts.length;
  const totalPallets = filteredCounts.reduce((sum, count) => sum + count.pallet_count, 0);
  const totalBricks = filteredCounts.reduce((sum, count) => sum + count.count_total, 0);
  const averagePalletsPerEntry = totalEntries > 0 ? Math.round(totalPallets / totalEntries) : 0;

  // Get recent entries (last 3)
  const recentEntries = filteredCounts.slice(0, 3);

  const periodText = {
    today: "Today",
    week: "This Week", 
    month: "This Month",
    year: "This Year"
  }[timeRange];

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-800 flex items-center gap-2">
          <Calculator size={20} />
          Hackline Count ({periodText})
        </CardTitle>
        <p className="text-sm text-slate-600">Pallets drying in the sun</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
          </div>
        ) : totalEntries > 0 ? (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-800">{totalPallets.toLocaleString()}</p>
                <p className="text-xs text-slate-500">Total Pallets</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-800">{totalBricks.toLocaleString()}</p>
                <p className="text-xs text-slate-500">Total Bricks</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-800">{averagePalletsPerEntry.toLocaleString()}</p>
                <p className="text-xs text-slate-500">Avg Pallets/Entry</p>
              </div>
            </div>

            {/* Recent Entries */}
            {recentEntries.length > 0 && (
              <div className="border-t pt-3">
                <h4 className="text-sm font-medium text-slate-700 mb-2">Recent Entries</h4>
                <div className="space-y-2">
                  {recentEntries.map(entry => (
                    <div key={entry.id} className="flex justify-between items-center text-sm">
                      <span className="text-slate-600">
                        {new Date(entry.date).toLocaleDateString()}
                      </span>
                      <div className="flex items-center gap-2">
                        <Package size={12} className="text-slate-400" />
                        <span className="font-medium text-slate-700">
                          {entry.pallet_count} {entry.pallet_type}
                        </span>
                        <span className="text-slate-500">
                          ({entry.count_total.toLocaleString()} bricks)
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Calculator className="h-12 w-12 text-slate-300 mx-auto mb-3" />
            <p className="text-slate-500 font-medium">No hackline counts</p>
            <p className="text-sm text-slate-400">for the selected period</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
