import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Calculator, Package } from "lucide-react";
import { HacklineCountPage } from "./HacklineCountPage";
import { FinishedProductsPage } from "./FinishedProductsPage";

export const ProductionTrackingPage = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Calculator className="h-8 w-8 text-slate-600" />
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Production Tracking</h1>
          <p className="text-slate-600">Track hackline counts and finished products</p>
        </div>
      </div>

      <Tabs defaultValue="hackline" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="hackline" className="flex items-center gap-2">
            <Calculator size={16} />
            Hackline Count
          </TabsTrigger>
          <TabsTrigger value="finished" className="flex items-center gap-2">
            <Package size={16} />
            Finished Products
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="hackline" className="mt-6">
          <HacklineCountPage />
        </TabsContent>
        
        <TabsContent value="finished" className="mt-6">
          <FinishedProductsPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};
