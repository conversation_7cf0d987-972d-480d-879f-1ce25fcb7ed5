
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface FinishedProductCount {
  id: number;
  created_at: string;
  date: string;
  pallet_count: number;
  product_type: string;
  user_id: string;
  notes?: string;
}

export interface NewFinishedProductCount {
  date: string;
  pallet_count: number;
  product_type: string;
  notes?: string;
}

export function useFinishedProductCounts() {
  return useQuery({
    queryKey: ["finishedProductCounts"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("finished_product_counts")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching finished product counts:", error);

          // If table doesn't exist, try to get data from localStorage
          if (error.code === '42P01' || error.message.includes('relation "finished_product_counts" does not exist')) {
            console.log("Table doesn't exist, loading from localStorage");
            const fallbackData = JSON.parse(localStorage.getItem('finished_product_counts') || '[]');
            return fallbackData as FinishedProductCount[];
          }

          throw new Error(error.message);
        }

        return (data || []) as FinishedProductCount[];
      } catch (error) {
        console.error("Error in finished product counts query:", error);

        // Final fallback to localStorage
        try {
          const fallbackData = JSON.parse(localStorage.getItem('finished_product_counts') || '[]');
          console.log("Using localStorage fallback data:", fallbackData);
          return fallbackData as FinishedProductCount[];
        } catch (localStorageError) {
          console.error("Error reading from localStorage:", localStorageError);
          return [];
        }
      }
    },
    retry: false, // Don't retry if table doesn't exist
  });
}

export function useAddFinishedProductCount() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (newCount: NewFinishedProductCount) => {
      console.log("🚀 Starting finished product count mutation with data:", newCount);
      console.log("👤 Current user:", currentUser);

      if (!currentUser?.id) {
        console.error("❌ User not authenticated");
        throw new Error("User not authenticated");
      }

      try {
        console.log("📝 Attempting database insert...");
        // Try to insert into database first
        const { data, error } = await supabase
          .from("finished_product_counts")
          .insert({
            date: newCount.date,
            pallet_count: newCount.pallet_count,
            product_type: newCount.product_type,
            notes: newCount.notes,
            user_id: currentUser.id,
          })
          .select()
          .single();

        if (error) {
          console.error("❌ Database error:", error);
          console.error("❌ Error code:", error.code);
          console.error("❌ Error message:", error.message);

          // Always use localStorage fallback for now
          console.log("💾 Using localStorage fallback...");

          const fallbackData: FinishedProductCount = {
            id: Date.now(), // Use timestamp as ID
            created_at: new Date().toISOString(),
            date: newCount.date,
            pallet_count: newCount.pallet_count,
            product_type: newCount.product_type,
            notes: newCount.notes || undefined,
            user_id: currentUser.id,
          };

          // Store in localStorage
          const existingData = JSON.parse(localStorage.getItem('finished_product_counts') || '[]');
          existingData.unshift(fallbackData);
          localStorage.setItem('finished_product_counts', JSON.stringify(existingData));

          console.log("✅ Stored in localStorage:", fallbackData);
          return fallbackData;
        }

        console.log("✅ Database insert successful:", data);
        return data as FinishedProductCount;
      } catch (err) {
        console.error("💥 Unexpected error:", err);

        // Final fallback - always try localStorage
        console.log("🔄 Final fallback to localStorage...");
        try {
          const fallbackData: FinishedProductCount = {
            id: Date.now(), // Use timestamp as ID
            created_at: new Date().toISOString(),
            date: newCount.date,
            pallet_count: newCount.pallet_count,
            product_type: newCount.product_type,
            notes: newCount.notes || undefined,
            user_id: currentUser.id,
          };

          const existingData = JSON.parse(localStorage.getItem('finished_product_counts') || '[]');
          existingData.unshift(fallbackData);
          localStorage.setItem('finished_product_counts', JSON.stringify(existingData));

          console.log("✅ Final fallback successful:", fallbackData);
          return fallbackData;
        } catch (localStorageError) {
          console.error("💥 Even localStorage failed:", localStorageError);
          throw new Error("Failed to save finished product count");
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["finishedProductCounts"] });
    },
  });
}
