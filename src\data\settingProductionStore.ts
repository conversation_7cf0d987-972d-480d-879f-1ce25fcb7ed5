import { supabase } from "@/integrations/supabase/client";
import { ManagementBrickType } from "./managementBrickTypes";
import { Database } from "@/integrations/supabase/types";

export type SettingProductionEntry = Database['public']['Tables']['setting_production_entries']['Row'];

let settingProductionEntries: SettingProductionEntry[] = [];
let listeners: (() => void)[] = [];

function emitChange() {
  for (const listener of listeners) {
    listener();
  }
}

async function fetchSettingProductionEntries() {
    const { data, error } = await supabase.from('setting_production_entries').select('*');
    if (error) {
        console.error("Error fetching setting production entries:", error);
        settingProductionEntries = [];
    } else {
        settingProductionEntries = data || [];
    }
    emitChange();
}

// Initial fetch
fetchSettingProductionEntries();


export async function addSettingProductionEntry(entry: { teamId: string; fireId: string; brickTypeId: string; palletCount: number; hour?: number; date?: string }) {
  console.log('[SettingProductionStore] Input entry:', entry);

  // First try with hour column
  let newEntry = {
    date: entry.date || new Date().toISOString().split('T')[0],
    team_id: entry.teamId,
    fire_id: entry.fireId,
    brick_type_id: entry.brickTypeId,
    pallet_count: entry.palletCount,
    hour: entry.hour,
  };

  console.log('[SettingProductionStore] Formatted entry for database:', newEntry);

  let { data, error } = await supabase.from("setting_production_entries").insert(newEntry).select();

  // If error is about hour column not existing, try without hour
  if (error && (error.message?.includes('hour') || error.message?.includes('column') || error.code === '42703')) {
    console.log('[SettingProductionStore] Retrying without hour column...');

    const newEntryWithoutHour = {
      date: entry.date || new Date().toISOString().split('T')[0],
      team_id: entry.teamId,
      fire_id: entry.fireId,
      brick_type_id: entry.brickTypeId,
      pallet_count: entry.palletCount,
    };

    console.log('[SettingProductionStore] Retrying with data:', newEntryWithoutHour);

    const result = await supabase.from("setting_production_entries").insert(newEntryWithoutHour).select();
    data = result.data;
    error = result.error;
  }

  if (error) {
    console.error("[SettingProductionStore] Database error:", error);
    console.error("[SettingProductionStore] Error details:", {
      message: error.message,
      details: error.details,
      hint: error.hint,
      code: error.code
    });

    // Provide more specific error messages
    if (error.code === '23503') {
      if (error.message.includes('team_id')) {
        throw new Error('Selected team does not exist. Please refresh the page and try again.');
      } else if (error.message.includes('fire_id')) {
        throw new Error('Selected fire/chamber does not exist. Please refresh the page and try again.');
      } else if (error.message.includes('brick_type_id')) {
        throw new Error('Selected brick type does not exist. Please refresh the page and try again.');
      }
    }

    throw new Error(error.message || 'Database error occurred');
  }

  console.log('[SettingProductionStore] Successfully inserted:', data);
  await fetchSettingProductionEntries();
}

export function getSettingProductionEntries(): SettingProductionEntry[] {
  return settingProductionEntries;
}

export function subscribeToSettingProduction(listener: () => void) {
  listeners.push(listener);
  return () => {
    listeners = listeners.filter(l => l !== listener);
  };
}

export async function getSettingSummary(
  timeFilter: (date: Date) => boolean,
  allFires: { id: string; name: string }[],
  allBrickTypes: ManagementBrickType[]
) {
  const { data: entries, error } = await supabase.from('setting_production_entries').select('*');

  if (error || !entries) {
    console.error("Error fetching setting production:", error);
    return [];
  }

  const filteredEntries = entries.filter(entry => timeFilter(new Date(entry.date)));

  const summaryByFire: Record<string, { pallets: number; bricks: number }> = {};

  filteredEntries.forEach(entry => {
    if (!summaryByFire[entry.fire_id]) {
      summaryByFire[entry.fire_id] = { pallets: 0, bricks: 0 };
    }

    const brickType = allBrickTypes.find(bt => bt.id === entry.brick_type_id);
    const bricksForEntry = brickType ? entry.pallet_count * brickType.bricks_per_pallet : 0;

    summaryByFire[entry.fire_id].pallets += entry.pallet_count;
    summaryByFire[entry.fire_id].bricks += bricksForEntry;
  });
  
  return Object.entries(summaryByFire).map(([fireId, data]) => {
      const fireInfo = allFires.find(f => f.id === fireId);
      return {
          fireId,
          fireName: fireInfo ? fireInfo.name : 'Unknown Fire',
          ...data,
      }
  });
}
